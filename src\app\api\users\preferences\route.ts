/**
 * User Preferences API Route
 * Production-grade user preferences endpoint with MCP integration
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { withAuth, withAuthAndValidation } from '@/lib/middleware/auth.middleware';
import { mcp_Memory_add_observations } from '@/lib/mcp-tools';
import { AuthenticatedRequest, ValidatedRequest } from '@/lib/middleware/auth.middleware';

// User preferences schema
const preferencesSchema = z.object({
    theme: z.enum(['light', 'dark', 'system']).optional(),
    sidebarCollapsed: z.boolean().optional(),
    dashboardLayout: z.enum(['grid', 'list']).optional(),
    notifications: z.object({
        email: z.boolean().optional(),
        push: z.boolean().optional(),
        inApp: z.boolean().optional()
    }).optional(),
    timezone: z.string().optional(),
    language: z.string().optional(),
    dateFormat: z.string().optional()
});

// GET handler - Retrieve user preferences
async function getHandler(req: AuthenticatedRequest): Promise<NextResponse> {
    try {
        if (!req.user) {
            return NextResponse.json(
                {
                    success: false,
                    message: 'Unauthorized',
                    code: 'UNAUTHORIZED'
                },
                { status: 401 }
            );
        }

        // Call backend API to get user preferences
        const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/users/${req.user.id}/preferences`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${req.cookies.get('access_token')?.value || ''}`
            }
        });

        if (!response.ok) {
            const errorData = await response.json();
            return NextResponse.json(
                {
                    success: false,
                    message: errorData.message || 'Failed to retrieve preferences',
                    code: 'API_ERROR'
                },
                { status: response.status }
            );
        }

        const data = await response.json();
        return NextResponse.json(data.preferences || {});
    } catch (error: any) {
        console.error('Get user preferences error:', error);

        return NextResponse.json(
            {
                success: false,
                message: error.message || 'Failed to retrieve preferences',
                code: error.code || 'API_ERROR'
            },
            { status: 500 }
        );
    }
}

// PUT handler - Update user preferences
async function putHandler(req: AuthenticatedRequest & ValidatedRequest): Promise<NextResponse> {
    try {
        if (!req.user) {
            return NextResponse.json(
                {
                    success: false,
                    message: 'Unauthorized',
                    code: 'UNAUTHORIZED'
                },
                { status: 401 }
            );
        }

        // Call backend API to update user preferences
        const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/users/${req.user.id}/preferences`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${req.cookies.get('access_token')?.value || ''}`
            },
            body: JSON.stringify(req.validatedBody)
        });

        if (!response.ok) {
            const errorData = await response.json();
            return NextResponse.json(
                {
                    success: false,
                    message: errorData.message || 'Failed to update preferences',
                    code: 'API_ERROR'
                },
                { status: response.status }
            );
        }

        // Track preference update in MCP Memory
        await mcp_Memory_add_observations({
            observations: [
                {
                    entityName: `user:${req.user.id}`,
                    contents: [
                        `User updated preferences at ${new Date().toISOString()}`,
                        `Updated preferences: ${JSON.stringify(req.validatedBody)}`
                    ]
                }
            ]
        });

        const data = await response.json();
        return NextResponse.json({
            success: true,
            preferences: data.preferences
        });
    } catch (error: any) {
        console.error('Update user preferences error:', error);

        return NextResponse.json(
            {
                success: false,
                message: error.message || 'Failed to update preferences',
                code: error.code || 'API_ERROR'
            },
            { status: 500 }
        );
    }
}

// Export with auth middleware
export const GET = withAuth(getHandler);
export const PUT = withAuthAndValidation(
    putHandler as any,
    {}, // No specific auth options
    { body: preferencesSchema }
);